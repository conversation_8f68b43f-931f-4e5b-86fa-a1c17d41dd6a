package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/4
 */
@AllArgsConstructor
@Getter
public enum TrafficType {

    UNKNOWN(0, "无"),
    NATURAL(1, "自然流量"),
    BUSINESS(2, "商业流量"),
    ;

    private final int code;

    private final String desc;

    public static TrafficType getByCode(Integer code) {
        for (TrafficType value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
