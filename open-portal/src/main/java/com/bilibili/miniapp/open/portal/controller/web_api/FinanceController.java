package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.FinanceControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.finance.FinanceDetailRespVo;
import com.bilibili.miniapp.open.portal.vo.finance.FinanceSaveReqVo;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.bo.finance.FinanceSaveReqBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 财务信息
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/finance")
public class FinanceController extends AbstractController {

    @Autowired
    private IFinanceService financeService;

    @PostMapping("/save")
    @MainSiteLoginValidation
    public Response<Void> saveFinanceInfo(Context context,
                                          @Valid @RequestBody FinanceSaveReqVo request) {

        FinanceSaveReqBo financeSaveReqBo = FinanceControllerMapper.MAPPER.voToBo(request);

        financeService.saveFinanceInfo(context.getMid(), financeSaveReqBo);

        return Response.SUCCESS();
    }

    @GetMapping("/detail")
    @MainSiteLoginValidation
    public Response<FinanceDetailRespVo> getFinanceDetail(Context context) {

        FinanceDetailBo financeDetailBo = financeService.getFinanceDetail(context.getMid());

        FinanceDetailRespVo response = FinanceControllerMapper.MAPPER.boToVo(financeDetailBo);

        return Response.SUCCESS(response);
    }
}
