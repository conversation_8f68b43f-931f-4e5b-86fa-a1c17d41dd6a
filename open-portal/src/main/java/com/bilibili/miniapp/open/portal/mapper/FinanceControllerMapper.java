package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.finance.FinanceDetailRespVo;
import com.bilibili.miniapp.open.portal.vo.finance.FinanceSaveReqVo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceSaveReqBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 财务控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface FinanceControllerMapper {

    FinanceControllerMapper MAPPER = Mappers.getMapper(FinanceControllerMapper.class);

    /**
     * 创建请求VO转BO
     */
    FinanceSaveReqBo voToBo(FinanceSaveReqVo vo);

    /**
     * 详情响应BO转VO
     */
    FinanceDetailRespVo boToVo(FinanceDetailBo bo);
}
