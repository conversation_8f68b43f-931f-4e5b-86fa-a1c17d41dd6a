package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.income.IncomeDetailQueryVo;
import com.bilibili.miniapp.open.portal.vo.income.IncomeDetailRespVo;
import com.bilibili.miniapp.open.portal.vo.income.IncomeDetailItemVo;
import com.bilibili.miniapp.open.portal.vo.income.IncomeSummaryVo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailQueryBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailItemBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 收入控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface IncomeControllerMapper {

    IncomeControllerMapper MAPPER = Mappers.getMapper(IncomeControllerMapper.class);

    /**
     * 收入汇总BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "withdrawableAmount", expression = "java(convertCentToYuan(bo.getWithdrawableAmount()))")
    @Mapping(target = "withdrawingAmount", expression = "java(convertCentToYuan(bo.getWithdrawingAmount()))")
    IncomeSummaryVo boToVo(IncomeSummaryBo bo);

    /**
     * 收入明细查询请求VO转BO
     */
    IncomeDetailQueryBo voToBo(IncomeDetailQueryVo vo);

    /**
     * 收入明细响应BO转VO
     */
    IncomeDetailRespVo boToVo(IncomeDetailBo bo);

    /**
     * 收入明细项BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "incomeAmount", expression = "java(convertCentToYuan(bo.getIncomeAmount()))")
    @Mapping(target = "channelFee", expression = "java(convertCentToYuan(bo.getChannelFee()))")
    @Mapping(target = "distributableIncomeAmount", expression = "java(convertCentToYuan(bo.getDistributableIncomeAmount()))")
    @Mapping(target = "actualIncomeAmount", expression = "java(convertCentToYuan(bo.getActualIncomeAmount()))")
    @Mapping(target = "channelFeeRatio", expression = "java(toPercentage(bo.getChannelFeeRatio()))")
    @Mapping(target = "distributableRatio", expression = "java(toPercentage(bo.getDistributableRatio()))")
    IncomeDetailItemVo itemBoToVo(IncomeDetailItemBo bo);

}
