package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIaaIncomeDetailPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 聚合日期 20250101
     */
    private String logDate;

    /**
     * 流量类型 1-自然流量，2-商业流量
     */
    private Integer trafficType;

    /**
     * 收入,单位（分）
     */
    private Long incomeAmount;

    /**
     * 通道费,单位（分）
     */
    private Long channelFee;

    /**
     * 可分成收入（收入-通道费）,单位（分）
     */
    private Long distributableIncomeAmount;

    /**
     * 开发者实际收入：可分成收入*分成比例,单位（分）
     */
    private Long actualIncomeAmount;

    /**
     * 提现状态，由预提单的提现状态决定：0-提现冻结，1-可提现，2-提现中，3-提现成功
     */
    private Integer withdrawStatus;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除，0-否，1-已删除
     */
    private Integer isDeleted;

    /**
     * 汇联易预提单id
     */
    private String accrualId;

    private static final long serialVersionUID = 1L;
}