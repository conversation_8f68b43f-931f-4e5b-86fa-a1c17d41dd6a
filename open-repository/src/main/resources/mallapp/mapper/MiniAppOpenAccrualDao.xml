<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAccrualDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="income_date" jdbcType="VARCHAR" property="incomeDate" />
    <result column="accrual_id" jdbcType="VARCHAR" property="accrualId" />
    <result column="withdraw_status" jdbcType="TINYINT" property="withdrawStatus" />
    <result column="total_amount" jdbcType="BIGINT" property="totalAmount" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, income_date, accrual_id, withdraw_status, total_amount, ctime, mtime, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_accrual
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_accrual
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_accrual
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample">
    delete from mini_app_open_accrual
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_accrual (app_id, income_date, accrual_id, 
      withdraw_status, total_amount, ctime, 
      mtime, is_deleted)
    values (#{appId,jdbcType=VARCHAR}, #{incomeDate,jdbcType=VARCHAR}, #{accrualId,jdbcType=VARCHAR}, 
      #{withdrawStatus,jdbcType=TINYINT}, #{totalAmount,jdbcType=BIGINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_accrual
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="incomeDate != null">
        income_date,
      </if>
      <if test="accrualId != null">
        accrual_id,
      </if>
      <if test="withdrawStatus != null">
        withdraw_status,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="incomeDate != null">
        #{incomeDate,jdbcType=VARCHAR},
      </if>
      <if test="accrualId != null">
        #{accrualId,jdbcType=VARCHAR},
      </if>
      <if test="withdrawStatus != null">
        #{withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_accrual
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_accrual
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.incomeDate != null">
        income_date = #{record.incomeDate,jdbcType=VARCHAR},
      </if>
      <if test="record.accrualId != null">
        accrual_id = #{record.accrualId,jdbcType=VARCHAR},
      </if>
      <if test="record.withdrawStatus != null">
        withdraw_status = #{record.withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_accrual
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      income_date = #{record.incomeDate,jdbcType=VARCHAR},
      accrual_id = #{record.accrualId,jdbcType=VARCHAR},
      withdraw_status = #{record.withdrawStatus,jdbcType=TINYINT},
      total_amount = #{record.totalAmount,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo">
    update mini_app_open_accrual
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="incomeDate != null">
        income_date = #{incomeDate,jdbcType=VARCHAR},
      </if>
      <if test="accrualId != null">
        accrual_id = #{accrualId,jdbcType=VARCHAR},
      </if>
      <if test="withdrawStatus != null">
        withdraw_status = #{withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo">
    update mini_app_open_accrual
    set app_id = #{appId,jdbcType=VARCHAR},
      income_date = #{incomeDate,jdbcType=VARCHAR},
      accrual_id = #{accrualId,jdbcType=VARCHAR},
      withdraw_status = #{withdrawStatus,jdbcType=TINYINT},
      total_amount = #{totalAmount,jdbcType=BIGINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_accrual (app_id, income_date, accrual_id, 
      withdraw_status, total_amount, ctime, 
      mtime, is_deleted)
    values (#{appId,jdbcType=VARCHAR}, #{incomeDate,jdbcType=VARCHAR}, #{accrualId,jdbcType=VARCHAR}, 
      #{withdrawStatus,jdbcType=TINYINT}, #{totalAmount,jdbcType=BIGINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      income_date = values(income_date),
      accrual_id = values(accrual_id),
      withdraw_status = values(withdraw_status),
      total_amount = values(total_amount),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_accrual
      (app_id,income_date,accrual_id,withdraw_status,total_amount,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.incomeDate,jdbcType=VARCHAR},
        #{item.accrualId,jdbcType=VARCHAR},
        #{item.withdrawStatus,jdbcType=TINYINT},
        #{item.totalAmount,jdbcType=BIGINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_accrual
      (app_id,income_date,accrual_id,withdraw_status,total_amount,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.incomeDate,jdbcType=VARCHAR},
        #{item.accrualId,jdbcType=VARCHAR},
        #{item.withdrawStatus,jdbcType=TINYINT},
        #{item.totalAmount,jdbcType=BIGINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      income_date = values(income_date),
      accrual_id = values(accrual_id),
      withdraw_status = values(withdraw_status),
      total_amount = values(total_amount),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_accrual
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="incomeDate != null">
        income_date,
      </if>
      <if test="accrualId != null">
        accrual_id,
      </if>
      <if test="withdrawStatus != null">
        withdraw_status,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="incomeDate != null">
        #{incomeDate,jdbcType=VARCHAR},
      </if>
      <if test="accrualId != null">
        #{accrualId,jdbcType=VARCHAR},
      </if>
      <if test="withdrawStatus != null">
        #{withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="incomeDate != null">
        income_date = values(income_date),
      </if>
      <if test="accrualId != null">
        accrual_id = values(accrual_id),
      </if>
      <if test="withdrawStatus != null">
        withdraw_status = values(withdraw_status),
      </if>
      <if test="totalAmount != null">
        total_amount = values(total_amount),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>