<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenIaaIncomeDetailDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="log_date" jdbcType="VARCHAR" property="logDate" />
    <result column="traffic_type" jdbcType="TINYINT" property="trafficType" />
    <result column="income_amount" jdbcType="BIGINT" property="incomeAmount" />
    <result column="channel_fee" jdbcType="BIGINT" property="channelFee" />
    <result column="distributable_income_amount" jdbcType="BIGINT" property="distributableIncomeAmount" />
    <result column="actual_income_amount" jdbcType="BIGINT" property="actualIncomeAmount" />
    <result column="withdraw_status" jdbcType="TINYINT" property="withdrawStatus" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="accrual_id" jdbcType="VARCHAR" property="accrualId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, log_date, traffic_type, income_amount, channel_fee, distributable_income_amount, 
    actual_income_amount, withdraw_status, extra, ctime, mtime, is_deleted, accrual_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_iaa_income_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_iaa_income_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_iaa_income_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample">
    delete from mini_app_open_iaa_income_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_iaa_income_detail (app_id, log_date, traffic_type, 
      income_amount, channel_fee, distributable_income_amount, 
      actual_income_amount, withdraw_status, extra, 
      ctime, mtime, is_deleted, 
      accrual_id)
    values (#{appId,jdbcType=VARCHAR}, #{logDate,jdbcType=VARCHAR}, #{trafficType,jdbcType=TINYINT}, 
      #{incomeAmount,jdbcType=BIGINT}, #{channelFee,jdbcType=BIGINT}, #{distributableIncomeAmount,jdbcType=BIGINT}, 
      #{actualIncomeAmount,jdbcType=BIGINT}, #{withdrawStatus,jdbcType=TINYINT}, #{extra,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accrualId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_iaa_income_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="trafficType != null">
        traffic_type,
      </if>
      <if test="incomeAmount != null">
        income_amount,
      </if>
      <if test="channelFee != null">
        channel_fee,
      </if>
      <if test="distributableIncomeAmount != null">
        distributable_income_amount,
      </if>
      <if test="actualIncomeAmount != null">
        actual_income_amount,
      </if>
      <if test="withdrawStatus != null">
        withdraw_status,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accrualId != null">
        accrual_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=VARCHAR},
      </if>
      <if test="trafficType != null">
        #{trafficType,jdbcType=TINYINT},
      </if>
      <if test="incomeAmount != null">
        #{incomeAmount,jdbcType=BIGINT},
      </if>
      <if test="channelFee != null">
        #{channelFee,jdbcType=BIGINT},
      </if>
      <if test="distributableIncomeAmount != null">
        #{distributableIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="actualIncomeAmount != null">
        #{actualIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="withdrawStatus != null">
        #{withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accrualId != null">
        #{accrualId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_iaa_income_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_iaa_income_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.logDate != null">
        log_date = #{record.logDate,jdbcType=VARCHAR},
      </if>
      <if test="record.trafficType != null">
        traffic_type = #{record.trafficType,jdbcType=TINYINT},
      </if>
      <if test="record.incomeAmount != null">
        income_amount = #{record.incomeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.channelFee != null">
        channel_fee = #{record.channelFee,jdbcType=BIGINT},
      </if>
      <if test="record.distributableIncomeAmount != null">
        distributable_income_amount = #{record.distributableIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.actualIncomeAmount != null">
        actual_income_amount = #{record.actualIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.withdrawStatus != null">
        withdraw_status = #{record.withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.accrualId != null">
        accrual_id = #{record.accrualId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_iaa_income_detail
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      log_date = #{record.logDate,jdbcType=VARCHAR},
      traffic_type = #{record.trafficType,jdbcType=TINYINT},
      income_amount = #{record.incomeAmount,jdbcType=BIGINT},
      channel_fee = #{record.channelFee,jdbcType=BIGINT},
      distributable_income_amount = #{record.distributableIncomeAmount,jdbcType=BIGINT},
      actual_income_amount = #{record.actualIncomeAmount,jdbcType=BIGINT},
      withdraw_status = #{record.withdrawStatus,jdbcType=TINYINT},
      extra = #{record.extra,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      accrual_id = #{record.accrualId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo">
    update mini_app_open_iaa_income_detail
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        log_date = #{logDate,jdbcType=VARCHAR},
      </if>
      <if test="trafficType != null">
        traffic_type = #{trafficType,jdbcType=TINYINT},
      </if>
      <if test="incomeAmount != null">
        income_amount = #{incomeAmount,jdbcType=BIGINT},
      </if>
      <if test="channelFee != null">
        channel_fee = #{channelFee,jdbcType=BIGINT},
      </if>
      <if test="distributableIncomeAmount != null">
        distributable_income_amount = #{distributableIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="actualIncomeAmount != null">
        actual_income_amount = #{actualIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="withdrawStatus != null">
        withdraw_status = #{withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accrualId != null">
        accrual_id = #{accrualId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo">
    update mini_app_open_iaa_income_detail
    set app_id = #{appId,jdbcType=VARCHAR},
      log_date = #{logDate,jdbcType=VARCHAR},
      traffic_type = #{trafficType,jdbcType=TINYINT},
      income_amount = #{incomeAmount,jdbcType=BIGINT},
      channel_fee = #{channelFee,jdbcType=BIGINT},
      distributable_income_amount = #{distributableIncomeAmount,jdbcType=BIGINT},
      actual_income_amount = #{actualIncomeAmount,jdbcType=BIGINT},
      withdraw_status = #{withdrawStatus,jdbcType=TINYINT},
      extra = #{extra,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      accrual_id = #{accrualId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_iaa_income_detail (app_id, log_date, traffic_type, 
      income_amount, channel_fee, distributable_income_amount, 
      actual_income_amount, withdraw_status, extra, 
      ctime, mtime, is_deleted, 
      accrual_id)
    values (#{appId,jdbcType=VARCHAR}, #{logDate,jdbcType=VARCHAR}, #{trafficType,jdbcType=TINYINT}, 
      #{incomeAmount,jdbcType=BIGINT}, #{channelFee,jdbcType=BIGINT}, #{distributableIncomeAmount,jdbcType=BIGINT}, 
      #{actualIncomeAmount,jdbcType=BIGINT}, #{withdrawStatus,jdbcType=TINYINT}, #{extra,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accrualId,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      log_date = values(log_date),
      traffic_type = values(traffic_type),
      income_amount = values(income_amount),
      channel_fee = values(channel_fee),
      distributable_income_amount = values(distributable_income_amount),
      actual_income_amount = values(actual_income_amount),
      withdraw_status = values(withdraw_status),
      extra = values(extra),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      accrual_id = values(accrual_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_iaa_income_detail
      (app_id,log_date,traffic_type,income_amount,channel_fee,distributable_income_amount,actual_income_amount,withdraw_status,extra,ctime,mtime,is_deleted,accrual_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.logDate,jdbcType=VARCHAR},
        #{item.trafficType,jdbcType=TINYINT},
        #{item.incomeAmount,jdbcType=BIGINT},
        #{item.channelFee,jdbcType=BIGINT},
        #{item.distributableIncomeAmount,jdbcType=BIGINT},
        #{item.actualIncomeAmount,jdbcType=BIGINT},
        #{item.withdrawStatus,jdbcType=TINYINT},
        #{item.extra,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accrualId,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_iaa_income_detail
      (app_id,log_date,traffic_type,income_amount,channel_fee,distributable_income_amount,actual_income_amount,withdraw_status,extra,ctime,mtime,is_deleted,accrual_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.logDate,jdbcType=VARCHAR},
        #{item.trafficType,jdbcType=TINYINT},
        #{item.incomeAmount,jdbcType=BIGINT},
        #{item.channelFee,jdbcType=BIGINT},
        #{item.distributableIncomeAmount,jdbcType=BIGINT},
        #{item.actualIncomeAmount,jdbcType=BIGINT},
        #{item.withdrawStatus,jdbcType=TINYINT},
        #{item.extra,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accrualId,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      log_date = values(log_date),
      traffic_type = values(traffic_type),
      income_amount = values(income_amount),
      channel_fee = values(channel_fee),
      distributable_income_amount = values(distributable_income_amount),
      actual_income_amount = values(actual_income_amount),
      withdraw_status = values(withdraw_status),
      extra = values(extra),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      accrual_id = values(accrual_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_iaa_income_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="trafficType != null">
        traffic_type,
      </if>
      <if test="incomeAmount != null">
        income_amount,
      </if>
      <if test="channelFee != null">
        channel_fee,
      </if>
      <if test="distributableIncomeAmount != null">
        distributable_income_amount,
      </if>
      <if test="actualIncomeAmount != null">
        actual_income_amount,
      </if>
      <if test="withdrawStatus != null">
        withdraw_status,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accrualId != null">
        accrual_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=VARCHAR},
      </if>
      <if test="trafficType != null">
        #{trafficType,jdbcType=TINYINT},
      </if>
      <if test="incomeAmount != null">
        #{incomeAmount,jdbcType=BIGINT},
      </if>
      <if test="channelFee != null">
        #{channelFee,jdbcType=BIGINT},
      </if>
      <if test="distributableIncomeAmount != null">
        #{distributableIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="actualIncomeAmount != null">
        #{actualIncomeAmount,jdbcType=BIGINT},
      </if>
      <if test="withdrawStatus != null">
        #{withdrawStatus,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accrualId != null">
        #{accrualId,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="logDate != null">
        log_date = values(log_date),
      </if>
      <if test="trafficType != null">
        traffic_type = values(traffic_type),
      </if>
      <if test="incomeAmount != null">
        income_amount = values(income_amount),
      </if>
      <if test="channelFee != null">
        channel_fee = values(channel_fee),
      </if>
      <if test="distributableIncomeAmount != null">
        distributable_income_amount = values(distributable_income_amount),
      </if>
      <if test="actualIncomeAmount != null">
        actual_income_amount = values(actual_income_amount),
      </if>
      <if test="withdrawStatus != null">
        withdraw_status = values(withdraw_status),
      </if>
      <if test="extra != null">
        extra = values(extra),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="accrualId != null">
        accrual_id = values(accrual_id),
      </if>
    </trim>
  </insert>
</mapper>