package com.bilibili.miniapp.open.service.biz.accrual.impl;

import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.SnowFlakeIdGenerator;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAccrualDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;
import com.bilibili.miniapp.open.service.biz.accrual.IAccrualService;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.vo.AccrualCreateMandatoryParams;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/6
 */
@Slf4j
@Service
public class AccrualService implements IAccrualService {

    @Autowired
    private MiniAppOpenAccrualDao accrualDao;

    @Autowired
    private HuilianyiPaymentService huilianyiPaymentService;

    @Autowired
    private IFinanceService financeService;

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Autowired
    private SnowFlakeIdGenerator snowFlakeIdGenerator;

    private static final String ACCRUAL_PREFIX = "MINI_APP_ACC_";

    @Override
    public List<MiniAppOpenAccrualPo> queryAccruals(String appId, WithdrawStatus withdrawStatus) {
        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andWithdrawStatusEqualTo(withdrawStatus.getCode())
                .andIsDeletedEqualTo(0);

        return accrualDao.selectByExample(example);
    }

    @Override
    public List<MiniAppOpenAccrualPo> queryAccruals(String appId, List<String> accrualIds) {
        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAccrualIdIn(accrualIds)
                .andIsDeletedEqualTo(0);

        return accrualDao.selectByExample(example);
    }


    @Override
    public String createAccrual(List<MiniAppOpenIaaIncomeDetailPo> incomeDetailPos) {

        AssertUtil.isTrue(!CollectionUtils.isEmpty(incomeDetailPos), ErrorCodeType.BAD_DATA.getCode(), "收入明细不能为空");

        MiniAppOpenIaaIncomeDetailPo anyOne = incomeDetailPos.get(0);
        String appId = anyOne.getAppId();
        String logDate = anyOne.getLogDate();

        boolean sameAppIdAndDate = incomeDetailPos.stream()
                .allMatch(po -> Objects.equals(appId, po.getAppId()) && Objects.equals(logDate, po.getLogDate()));
        AssertUtil.isTrue(sameAppIdAndDate, ErrorCodeType.BAD_DATA.getCode(), "收入明细不能属于不同的appId或者不同的日期");

        long totalAmount = incomeDetailPos.stream()
                .map(MiniAppOpenIaaIncomeDetailPo::getActualIncomeAmount)
                .reduce(0L, Long::sum);

        ChannelMiniAppInfoDTO appInfo = miniAppRemoteService.queryAppInfoWithinCache(appId);
        FinanceDetailBo financeDetail = financeService.getFinanceDetail(appInfo.getMid());
        if (financeDetail == null) {
            log.info("[创建预提单[appId:{}]] 未创建财务信息，无法生成预提单", appId);
            return null;
        }

        return doCreateAccrual(financeDetail, appId, totalAmount);
    }


    private String doCreateAccrual(FinanceDetailBo financeDetail, String appId, long totalAmount) {
        AccrualCreateMandatoryParams query = new AccrualCreateMandatoryParams()
                .setBusinessCode(ACCRUAL_PREFIX + snowFlakeIdGenerator.nextId())
                .setPayee(financeDetail.getBankInfo().getBankAccountNumber())
                .setAmtInCny(BigDecimal.valueOf(totalAmount));

        HuilianyiAccrualCreateResult result = huilianyiPaymentService.createAccrual(query);
        String successCode = "0000";
        if (Objects.equals(successCode, result.getErrorCode())) {
            log.warn("[创建预提单[appId:{}]] 创建预提单失败，result={}", appId, result);
        }
        return result.getKey();
    }

    @Override
    public void updateStatus(List<String> accrualIds, WithdrawStatus sourceStatus, WithdrawStatus targetStatus) {
        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAccrualIdIn(accrualIds)
                .andWithdrawStatusEqualTo(sourceStatus.getCode())
                .andIsDeletedEqualTo(0);

        MiniAppOpenAccrualPo updatePo = new MiniAppOpenAccrualPo();
        updatePo.setWithdrawStatus(targetStatus.getCode());

        int updateSize = accrualDao.updateByExampleSelective(updatePo, example);
        AssertUtil.isTrue(updateSize == accrualIds.size(), ErrorCodeType.BAD_DATA.getCode(), "更新预提单状态失败");
    }

}
