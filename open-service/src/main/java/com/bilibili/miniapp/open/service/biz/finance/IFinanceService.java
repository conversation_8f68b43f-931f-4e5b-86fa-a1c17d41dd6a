package com.bilibili.miniapp.open.service.biz.finance;

import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceSaveReqBo;

/**
 * 财务服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface IFinanceService {

    /**
     * 创建财务信息
     *
     * @param mid 用户ID
     * @param financeSaveReqBo 财务信息创建请求
     */
    void saveFinanceInfo(Long mid, FinanceSaveReqBo financeSaveReqBo);

    /**
     * 获取财务信息详情
     *
     * @param mid 用户ID
     * @return 财务信息详情
     */
    FinanceDetailBo getFinanceDetail(Long mid);
}
