package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.BFSKey;
import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.*;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSettlementDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.accrual.IAccrualService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.biz.resource.IBFSService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.ISettlementService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailBo;
import com.bilibili.miniapp.open.service.bo.settlement.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.SettlementConfig;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateResult;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiUploadResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结算服务实现
 * 金额单位为分保留2位小数
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class SettlementService implements ISettlementService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private MiniAppOpenSettlementDao settlementDao;

    @Autowired
    private IFinanceService financeService;

    @Autowired
    private IBFSService bfsService;

    @Autowired
    private HuilianyiPaymentService huilianyiPaymentService;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private IAccrualService accrualService;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 固定比例：(1+6%)，用作计算金额
     */
    private final BigDecimal fixedProportionRatio = new BigDecimal("0.06").add(BigDecimal.ONE);

    @Override
    public SettlementDateListBo getSettlementDates(Long mid, String appId) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        List<MiniAppOpenAccrualPo> accrualList = accrualService.queryAccruals(appId, WithdrawStatus.WITHDRAWABLE);

        List<SettlementDateBo> settlementList = accrualList.stream()
                .map(po -> SettlementDateBo.builder()
                        .date(po.getIncomeDate())
                        .accrualId(po.getAccrualId())
                        .build())
                .collect(Collectors.toList());

        return SettlementDateListBo.builder()
                .settlementList(settlementList)
                .build();
    }

    @Override
    public SettlementPreviewBo getSettlementPreview(Long mid, String appId, List<String> accrualIds) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        if (CollectionUtils.isEmpty(accrualIds)) {
            return SettlementPreviewBo.emptyInstance();
        }

        List<MiniAppOpenAccrualPo> accrualList = accrualService.queryAccruals(appId, accrualIds);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(accrualList), ErrorCodeType.NO_DATA.getCode(), "未找到对应的预提单数据");

        Long withdrawApplyAmount = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);

        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null && financeDetail.getInvoiceInfo() != null, ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");

        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getTaxType());

        Long taxFee = calculateTaxFee(withdrawApplyAmount, taxType.getTaxRatio());

        Long actualWithdrawAmount = calculateActualWithdrawAmount(withdrawApplyAmount, taxType.getTaxRatio());

        return SettlementPreviewBo.builder()
                .withdrawApplyAmount(withdrawApplyAmount)
                .taxFee(taxFee)
                .actualWithdrawAmount(actualWithdrawAmount)
                .build();
    }

    /**
     * 由于可支持的文件类型比较多，需要解析文件类型映射到http media type的逻辑会比较复杂，直接使用bfs url返回的类型
     */
    @Override
    public InvoiceUploadBo uploadInvoice(MultipartFile file) {
        try {
            byte[] fileBytes = file.getBytes();
            BFSUploadResult bfsUploadResult = bfsService.upload(BFSKey.CATEGORY_MINIAPP_OPEN, file.getOriginalFilename(), fileBytes);
            log.info("BFS上传成功，url: {}", bfsUploadResult.getUrl());

            AssertUtil.isTrue(StringUtils.isNotBlank(bfsUploadResult.getUrl()), ErrorCodeType.NO_DATA.getCode(), "上传后未返回图片url");

            String partName = getBfsFileNameFromUrl(bfsUploadResult.getUrl());

            HuilianyiUploadResult huilianyiResult = huilianyiPaymentService.uploadAttachments(bfsUploadResult.getUrl(), partName);
            log.info("汇联易上传成功，oid: {}", huilianyiResult.getOid());

            return InvoiceUploadBo.builder()
                    .oid(huilianyiResult.getOid())
                    .url(bfsUploadResult.getUrl())
                    .build();

        } catch (Exception e) {
            log.error("发票上传失败", e);
            throw new ServiceException("发票上传失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<SettlementItemBo> getSettlementList(Long mid,
                                                          String appId,
                                                          Integer page,
                                                          Integer size,
                                                          Long beginTime,
                                                          Long endTime,
                                                          Integer settlementStatus) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        MiniAppOpenSettlementPoExample.Criteria criteria = example.createCriteria();

        criteria.andAppIdEqualTo(appId);

        if (beginTime != null) {
            criteria.andSettlementBeginTimeGreaterThanOrEqualTo(new Timestamp(beginTime));
        }

        if (endTime != null) {
            criteria.andSettlementEndTimeLessThanOrEqualTo(new Timestamp(endTime));
        }

        if (settlementStatus != null) {
            criteria.andSettlementStatusEqualTo(settlementStatus);
        }

        criteria.andIsDeletedEqualTo(0);

        long total = settlementDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        Page pageInfo = Page.valueOf(page, size);
        example.setLimit(pageInfo.getLimit());
        example.setOffset(pageInfo.getOffset());
        example.setOrderByClause("id desc");

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);


        List<SettlementItemBo> settlementItemList = settlementList.stream()
                .map(this::convertToSettlementItemBo)
                .collect(Collectors.toList());

        return new PageResult<>((int) total, settlementItemList);
    }

    @Override
    public SettlementDetailBo getSettlementDetail(Long mid, String settlementId) {
        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(settlementList), ErrorCodeType.NO_DATA.getCode(), "结算单不存在");

        MiniAppOpenSettlementPo settlement = settlementList.get(0);

        AssertUtil.isTrue(accountService.hasPermission(mid, settlement.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        CompanyDetailBo detail = companyService.getDetail(mid);

        return SettlementDetailBo.builder()
                .settlementId(settlement.getSettlementId())
                .settlementBeginTime(settlement.getSettlementBeginTime())
                .settlementEndTime(settlement.getSettlementEndTime())
                .companyName(detail.getCompanyInfo().getCompanyName())
                .actualWithdrawAmount(settlement.getActualWithdrawAmount())
                .settlementStatus(settlement.getSettlementStatus())
                .paymentOrderId(settlement.getPaymentOrderId())
                .build();
    }

    private SettlementItemBo convertToSettlementItemBo(MiniAppOpenSettlementPo po) {
        return SettlementItemBo.builder()
                .settlementId(po.getSettlementId())
                .appId(po.getAppId())
                .settlementBeginTime(po.getSettlementBeginTime())
                .settlementEndTime(po.getSettlementEndTime())
                .settlementStatus(po.getSettlementStatus())
                .withdrawApplyAmount(po.getWithdrawApplyAmount())
                .actualWithdrawAmount(po.getActualWithdrawAmount())
                .taxFee(po.getTaxFee())
                .build();
    }

    private String getBfsFileNameFromUrl(String url) {
        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url).build();
        List<String> pathSegments = uriComponents.getPathSegments();
        return pathSegments.get(pathSegments.size() - 1);
    }

    /**
     * 计算税费：withdraw_apply_amount/(1+6%)*税率，进行四舍五入，过程保留4位
     */
    private Long calculateTaxFee(Long withdrawApplyAmount, String taxRatio) {
        BigDecimal amount = new BigDecimal(withdrawApplyAmount);
        BigDecimal result = amount
                .divide(fixedProportionRatio, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(taxRatio));
        return result.setScale(0, RoundingMode.HALF_UP).longValue();
    }

    /**
     * 计算实际提现金额：withdraw_apply_amount/(1+6%)*(1+税率)，进行四舍五入，过程保留4位
     */
    private Long calculateActualWithdrawAmount(Long withdrawApplyAmount, String taxRatio) {
        BigDecimal amount = new BigDecimal(withdrawApplyAmount);
        BigDecimal result = amount
                .divide(fixedProportionRatio, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.ONE.add(new BigDecimal(taxRatio)));
        return result.setScale(0, RoundingMode.HALF_UP).longValue();
    }

    @Override
    public void confirmSettlement(Long mid, String settlementId) {
        updateSettlementStatus(mid, settlementId, SettlementStatus.PENDING_UPLOAD_INVOICE, SettlementStatus.PENDING_CONFIRMATION);
    }

    @Override
    public void cancelSettlement(Long mid, String settlementId) {
        updateSettlementStatus(mid, settlementId, SettlementStatus.PENDING_UPLOAD_INVOICE, SettlementStatus.CANCELED);
    }

    private void updateSettlementStatus(Long mid, String settlementId, SettlementStatus sourceStatus, SettlementStatus targetStatus) {
        MiniAppOpenSettlementPo settlement = getSettlement(settlementId);
        AssertUtil.isTrue(settlement != null, ErrorCodeType.NO_DATA.getCode(), "结算单不存在");
        AssertUtil.isTrue(accountService.hasPermission(mid, settlement.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);
        AssertUtil.isTrue(settlement.getSettlementStatus() == sourceStatus.getCode(), ErrorCodeType.NO_DATA.getCode(), "结算单状态不正确");

        MiniAppOpenSettlementPoExample updateExample = new MiniAppOpenSettlementPoExample();
        updateExample.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andSettlementStatusEqualTo(sourceStatus.getCode())
                .andIsDeletedEqualTo(0);

        MiniAppOpenSettlementPo updateRecord = new MiniAppOpenSettlementPo();
        updateRecord.setSettlementStatus(targetStatus.getCode());

        settlementDao.updateByExampleSelective(updateRecord, updateExample);
    }

    private MiniAppOpenSettlementPo getSettlement(String settlementId) {
        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        if (CollectionUtils.isEmpty(settlementList)) {
            return null;
        }
        return settlementList.get(0);
    }

    @Override
    @LockRequest(key = "'create_settlement_'+#request.getAccrualIds()")
    public void createSettlement(Long mid, SettlementCreateReqBo request) {

        AssertUtil.isTrue(!CollectionUtils.isEmpty(request.getAccrualIds()), ErrorCodeType.BAD_PARAMETER.getCode(), "预提单ID列表不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInvoiceOid()), ErrorCodeType.BAD_PARAMETER.getCode(), "发票OID不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInvoiceUrl()), ErrorCodeType.BAD_PARAMETER.getCode(), "发票URL不能为空");

        String appId = request.getAppId();
        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        List<MiniAppOpenAccrualPo> accrualList = accrualService.queryAccruals(request.getAppId(), request.getAccrualIds());
        AssertUtil.isTrue(!CollectionUtils.isEmpty(accrualList), ErrorCodeType.NO_DATA.getCode(), "未找到对应的预提单数据");
        AssertUtil.isTrue(accrualList.size() == request.getAccrualIds().size(), ErrorCodeType.BAD_PARAMETER.getCode(), "部分预提单不存在");

        boolean allWithdrawable = accrualList.stream().allMatch(accrual -> Objects.equals(accrual.getWithdrawStatus(), WithdrawStatus.WITHDRAWABLE.getCode()));
        AssertUtil.isTrue(allWithdrawable, ErrorCodeType.BAD_DATA.getCode(), "所有预提单必须处于可提现状态");

        validateAccrualDatesContinuity(accrualList);

        FinanceDetailBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null && financeDetail.getInvoiceInfo() != null,
                ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");

        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getTaxType());

        Long withdrawApplyAmount = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);

        Long taxFee = calculateTaxFee(withdrawApplyAmount, taxType.getTaxRatio());
        Long actualWithdrawAmount = calculateActualWithdrawAmount(withdrawApplyAmount, taxType.getTaxRatio());

        ((SettlementService) AopContext.currentProxy()).createSettlementRecord(appId, accrualList, withdrawApplyAmount, taxFee, actualWithdrawAmount,
                request.getInvoiceOid(), request.getInvoiceUrl(), taxType.getTaxRatio());
    }


    @Override
    public InvoiceIssuanceBaseInfoBo getInvoiceBaseInfo() {
        SettlementConfig settlementConfig = configCenter.getSettlementConfig();
        return InvoiceIssuanceBaseInfoBo.builder()
                .bankCode(settlementConfig.getBankCode())
                .bankName(settlementConfig.getBankName())
                .companyName(settlementConfig.getCompanyName())
                .taxpayerIdentificationNumber(settlementConfig.getTaxpayerIdentificationNumber())
                .build();
    }

    /**
     * 校验预提单时间连续性
     */
    private void validateAccrualDatesContinuity(List<MiniAppOpenAccrualPo> accrualList) {
        List<String> sortedDates = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getIncomeDate)
                .sorted()
                .collect(Collectors.toList());

        for (int i = 1; i < sortedDates.size(); i++) {
            String prevDate = sortedDates.get(i - 1);
            String currDate = sortedDates.get(i);

            try {
                LocalDate prevLocalDate = LocalDate.parse(prevDate, FORMATTER);
                LocalDate currLocalDate = LocalDate.parse(currDate, FORMATTER);

                if (!currLocalDate.equals(prevLocalDate.plusDays(1))) {
                    throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(),
                            String.format("预提单日期不连续，%s 和 %s 之间有间隔", prevDate, currDate));
                }
            } catch (DateTimeParseException e) {
                throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "预提单日期格式错误");
            }
        }
    }

    /**
     * 创建结算单记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void createSettlementRecord(String appId, List<MiniAppOpenAccrualPo> accrualList,
                                       Long withdrawApplyAmount, Long taxFee, Long actualWithdrawAmount,
                                       String invoiceOid, String invoiceUrl, String taxRatio) {

        List<String> sortedDates = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getIncomeDate)
                .sorted()
                .collect(Collectors.toList());

        Timestamp settlementBeginTime = parseIncomeDate(sortedDates.get(0));
        Timestamp settlementEndTime = parseIncomeDate(sortedDates.get(sortedDates.size() - 1));

        String settlementId = generateSettlementId();

        MiniAppOpenSettlementPo settlementPo = MiniAppOpenSettlementPo.builder()
                .appId(appId)
                .settlementBeginTime(settlementBeginTime)
                .settlementEndTime(settlementEndTime)
                .settlementId(settlementId)
                .settlementStatus(SettlementStatus.PENDING_CONFIRMATION.getCode())
                .withdrawApplyAmount(withdrawApplyAmount)
                .actualWithdrawAmount(actualWithdrawAmount)
                .taxFee(taxFee)
                .invoiceOid(invoiceOid)
                .invoiceUrl(invoiceUrl)
                .extra(JSON.toJSONString(SettlementExtraBo.builder()
                        .taxRatio(taxRatio)
                        .build()))
                .build();

        settlementDao.insertSelective(settlementPo);

        List<String> accrualIds = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getAccrualId)
                .collect(Collectors.toList());

        accrualService.updateStatus(accrualIds, WithdrawStatus.WITHDRAWABLE, WithdrawStatus.WITHDRAWING);
    }

    /**
     * 解析收入日期字符串为Timestamp
     */
    private Timestamp parseIncomeDate(String incomeDate) {
        try {
            LocalDate date = LocalDate.parse(incomeDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            return Timestamp.valueOf(date.atStartOfDay());
        } catch (DateTimeParseException e) {
            throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "日期格式错误: " + incomeDate);
        }
    }

    /**
     * 生成结算单ID
     */
    private String generateSettlementId() {
        return "SETTLEMENT_" + System.currentTimeMillis() + "_" + (int) (Math.random() * 1000);
    }
}
