package com.bilibili.miniapp.open.service.bo.settlement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 结算详情BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementDetailBo {

    /**
     * 结算单ID
     */
    private String settlementId;

    /**
     * 结算开始时间
     */
    private Timestamp beginTime;

    /**
     * 结算结束时间
     */
    private Timestamp endTime;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 实际提现金额（分）
     */
    private Long actualWithdrawAmount;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 汇联易付款单id
     */
    private String paymentOrderId;
}
