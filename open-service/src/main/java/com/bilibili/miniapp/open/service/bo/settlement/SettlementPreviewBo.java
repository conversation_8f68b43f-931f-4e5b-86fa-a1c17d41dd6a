package com.bilibili.miniapp.open.service.bo.settlement;

import lombok.Builder;
import lombok.Data;

/**
 * 结算预览响应BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
public class SettlementPreviewBo {

    /**
     * 申请提现金额
     */
    private Long withdrawApplyAmount;

    /**
     * 税费
     */
    private Long taxFee;

    /**
     * 实际提现金额
     */
    private Long actualWithdrawAmount;

    public static SettlementPreviewBo emptyInstance() {
        return SettlementPreviewBo.builder()
                .withdrawApplyAmount(0L)
                .taxFee(0L)
                .actualWithdrawAmount(0L)
                .build();
    }
}
